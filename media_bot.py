"""
Точка входа для Telegram-бота "sh: Media"
Содержит основную логику запуска бота
"""

import asyncio
import logging
from aiogram import Bo<PERSON>, Dispatcher
from aiogram.filters import Command
from aiogram.types import Message

from media_config import TELEGRAM_BOT_TOKEN, LOG_LEVEL, LOG_FORMAT


# Настройка логирования
logging.basicConfig(
    level=getattr(logging, LOG_LEVEL),
    format=LOG_FORMAT
)
logger = logging.getLogger(__name__)


# Создание экземпляров бота и диспетчера
bot = Bot(token=TELEGRAM_BOT_TOKEN)
dp = Dispatcher()


@dp.message(Command("start"))
async def cmd_start(message: Message):
    """Обработчик команды /start"""
    welcome_text = (
        "🎨 Добро пожаловать в sh: Media!\n\n"
        "Я умею:\n"
        "• `/img <описание>` — генерировать изображения\n"
        "• Редактировать изображения (ответьте на фото с `/img <описание>`)\n\n"
        "Попробуйте: `/img cat astronaut`"
    )
    await message.answer(welcome_text)
    logger.info(f"Пользователь {message.from_user.id} запустил бота")


async def main():
    """Основная функция запуска бота"""
    logger.info("Запуск бота sh: Media...")
    
    # Проверяем наличие токена
    if not TELEGRAM_BOT_TOKEN:
        logger.error("TELEGRAM_BOT_TOKEN не установлен в media_config.py")
        return
    
    try:
        # Запускаем бота
        await dp.start_polling(bot)
    except Exception as e:
        logger.error(f"Ошибка при запуске бота: {e}")
    finally:
        await bot.session.close()


if __name__ == "__main__":
    asyncio.run(main())
